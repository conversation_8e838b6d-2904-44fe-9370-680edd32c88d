<!DOCTYPE html>
<html>
<head>
    <style>
        /* Set the size of the div element that contains the map */
        #map {
            height: 400px;
            width: 600px;
        }
    </style>
</head>
<body>

<div id="floating-panel">
    <b>Mode of Travel: </b>
    <select id="mode">
        <option value="DRIVING">Driving</option>
        <option value="WALKING">Walking</option>
        <option value="BICYCLING">Bicycling</option>
        <option value="TRANSIT">Transit</option>
    </select>
</div>


<!--The div elements for the map and message -->
<div id="map"></div>
<div id="msg"></div>
<script>
    function haversine_distance(mk1, mk2) {
        var R = 3958.8; // Radius of the Earth in miles
        var rlat1 = mk1.position.lat() * (Math.PI/180); // Convert degrees to radians
        var rlat2 = mk2.position.lat() * (Math.PI/180); // Convert degrees to radians
        var difflat = rlat2-rlat1; // Radian difference (latitudes)
        var difflon = (mk2.position.lng()-mk1.position.lng()) * (Math.PI/180); // Radian difference (longitudes)

        var d = 2 * R * Math.asin(Math.sqrt(Math.sin(difflat/2)*Math.sin(difflat/2)+Math.cos(rlat1)*Math.cos(rlat2)*Math.sin(difflon/2)*Math.sin(difflon/2)));
        return d;
    }
    // Initialize and add the map
    var map;
    function initMap() {

        // The map, centered on Central Park
        const center = {lat: 40.774102, lng: -73.971734};
        const options = {zoom: 15, scaleControl: true, center: center};
        map = new google.maps.Map(
            document.getElementById('map'), options);
        // Locations of landmarks
        const dakota = {lat: 29.3500, lng: 48.0283};
        const frick = {lat: 29.3833, lng: 47.9897};
        // The markers for The Dakota and The Frick Collection
        var mk1 = new google.maps.Marker({position: dakota, map: map});
        var mk2 = new google.maps.Marker({position: frick, map: map});

        // Calculate and display the distance between markers
        var distance = haversine_distance(mk1, mk2);
        document.getElementById('msg').innerHTML = "Distance between markers: " + distance.toFixed(2) + " mi.";


        let directionsService = new google.maps.DirectionsService();
        let directionsRenderer = new google.maps.DirectionsRenderer();
        directionsRenderer.setMap(map); // Existing map object displays directions
        // Create route from existing points used for markers

        document.getElementById("mode").addEventListener("change", () => {

        });
        const route = {
            origin: dakota,
            destination: frick,
            travelMode: 'TRANSIT'
        }

        directionsService.route(route,
            function(response, status) { // anonymous function to capture directions
                if (status !== 'OK') {
                    window.alert('Directions request failed due to ' + status);
                    return;
                } else {
                    directionsRenderer.setDirections(response); // Add route to the map
                    var directionsData = response.routes[0].legs[0]; // Get data about the mapped route
                    if (!directionsData) {
                        window.alert('Directions request failed');
                        return;
                    }
                    else {
                        document.getElementById('msg').innerHTML += " Driving distance is " + directionsData.distance.text + " (" + directionsData.duration.text + ").";
                    }
                }
            });


    }
</script>
<!--Load the API from the specified URL -- remember to replace YOUR_API_KEY-->
<script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA_foD6VvulHSpxKYjtgehkQ_UoVGHH64Y&callback=initMap">
</script>
</body>
</html>
