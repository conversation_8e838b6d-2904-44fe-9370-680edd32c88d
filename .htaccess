


# Handle Authorization Header
 RewriteCond %{HTTP:Authorization} .
 RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]


# RewriteEngine On
RewriteCond %{HTTPS} !on
RewriteCond %{REQUEST_URI} !^/[0-9]+\..+\.cpaneldcv$
RewriteCond %{REQUEST_URI} !^/\.well-known/pki-validation/[A-F0-9]{32}\.txt(?:\ Comodo\ DCV)?$
RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# <IfModule mod_rewrite.c>
#     RewriteEngine On

#     # Force SSL
#     RewriteCond %{HTTPS} !=on
#     RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
#     # Remove public folder form URL
#     RewriteRule ^(.*)$ public/$1 [L]
# </IfModule>


# <IfModule mod_rewrite.c>
#        RewriteCond %{REQUEST_URI} !^/.well-known
#    </IfModule>

#
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteCond %{REQUEST_URI} !^/.well-known
    RewriteRule ^(.*)$ public/$1
</IfModule>

<IfModule php7_module>
   php_flag display_errors Off
   php_value max_execution_time 30
   php_value max_input_time 60
   php_value max_input_vars 1000
   php_value memory_limit 10000M
   php_value post_max_size 100M
   php_value session.gc_maxlifetime 1440
   php_value session.save_path "/var/cpanel/php/sessions/ea-php73"
   php_value upload_max_filesize 100M
   php_flag zlib.output_compression Off
</IfModule>
<IfModule lsapi_module>
   php_flag display_errors Off
   php_value max_execution_time 30
   php_value max_input_time 60
   php_value max_input_vars 1000
   php_value memory_limit 10000M
   php_value post_max_size 100M
   php_value session.gc_maxlifetime 1440
   php_value session.save_path "/var/cpanel/php/sessions/ea-php73"
   php_value upload_max_filesize 100M
   php_flag zlib.output_compression Off
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php80” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php80 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
