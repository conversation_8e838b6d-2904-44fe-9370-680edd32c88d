<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
Route::group(['middleware' => ['lang' , 'checkCurrency']], function() {

    // user component api login
    Route::post('login', [\App\Http\Controllers\Api\UserController::class, 'login']);  // login route
    Route::post('register', [\App\Http\Controllers\Api\UserController::class, 'register']); // register route
    Route::post('resend', [\App\Http\Controllers\Api\UserController::class, 'resend']); // send code
    Route::post('verificationPhone', [\App\Http\Controllers\Api\UserController::class, 'verificationPhone']); // activeation code
    Route::post('forgetPassword', [\App\Http\Controllers\Api\UserController::class, 'forgetPassword']); // activeation code
    Route::post('confirmNewPassword', [\App\Http\Controllers\Api\UserController::class, 'confirmNewPassword']); // activeation code
// profile route
    Route::get('profile', [\App\Http\Controllers\Api\UserController::class, 'profile']); // profile
    Route::post('updateProfile', [\App\Http\Controllers\Api\UserController::class, 'updateProfile']); // update profile

    // address

    /**
     *  Address
     **/
    Route::get('countries',     [\App\Http\Controllers\Api\AddressController::class, 'countries']); // activeation code
    Route::get('governmnets', [\App\Http\Controllers\Api\AddressController::class, 'governmnets']);
    Route::post('addNewAddress', [\App\Http\Controllers\Api\AddressController::class, 'addNewAddress']);
    Route::get('getAddress', [\App\Http\Controllers\Api\AddressController::class, 'getAddress']);
    Route::post('updateAddress/{id}', [\App\Http\Controllers\Api\AddressController::class, 'updateAddress']);
    Route::post('deleteAddress/{id}', [\App\Http\Controllers\Api\AddressController::class, 'deleteAddress']);
    Route::post('choose/address', [\App\Http\Controllers\Api\AddressController::class, 'chooseaddress']);

///========================================= End of auth controller =======================================================
///
///
/// ///========================================= start of main controller =======================================================
    Route::get('main', [\App\Http\Controllers\Api\MainController::class, 'main']); // activeation code
    Route::get('search', [\App\Http\Controllers\Api\MainController::class, 'search']); // activeation code
    Route::get('notifcations', [\App\Http\Controllers\Api\MainController::class, 'notifcations']); // activeation code
    Route::get('more', [\App\Http\Controllers\Api\MainController::class, 'more']); // activeation code

    Route::get('categories', [\App\Http\Controllers\Api\MainController::class, 'categories']); // get main categoris
    Route::get('categories/{id}', [\App\Http\Controllers\Api\MainController::class, 'sub_categories']); // get sub categories categoris
    Route::get('productByCategories/{id}', [\App\Http\Controllers\Api\MainController::class, 'productByCategories']); // get sub categories categoris

    // get the vendor detials

    Route::get('tagers', [\App\Http\Controllers\Api\MainController::class, 'tagers']); // get sub categories categoris
    Route::get('vendorCategory/{id}', [\App\Http\Controllers\Api\MainController::class, 'vendorCategory']); // get sub categories categoris
    Route::get('vendorProductsCategory/{id}', [\App\Http\Controllers\Api\MainController::class, 'vendorProductsCategory']); // get sub categories categoris


    Route::get('product/{id}', [\App\Http\Controllers\Api\MainController::class, 'getproduct']); // get sub categories categoris

    Route::post('contact',[\App\Http\Controllers\Api\MainController::class, 'contact']);
    Route::get('contactPage',[\App\Http\Controllers\Api\MainController::class, 'contactPage']);
    Route::get('brands',[\App\Http\Controllers\Api\MainController::class, 'brands']);



    #==================================added cart =========================================#
    // add cart
    Route::post('add/cart',[\App\Http\Controllers\Api\CartController::class, 'addCart']);
    //get the content Cart
    Route::post('get/cart',[\App\Http\Controllers\Api\CartController::class, 'getCart']);

    // get product related free
    Route::post('coupons',[\App\Http\Controllers\Api\CartController::class, 'coupons']);


    //edit the content Cart
    Route::post('edit/cart',[\App\Http\Controllers\Api\CartController::class, 'editCart']);
    //delete the delete Cart
    Route::post('delete/cart',[\App\Http\Controllers\Api\CartController::class, 'deleteCart']);

    Route::post('confirmOrder',[\App\Http\Controllers\Api\CartController::class, 'confirmOrder']);

    //arrival time
    Route::get('ArrivalDate',[\App\Http\Controllers\Api\CartController::class, 'ArrivalDate']);
    Route::get('arrivalTime',[\App\Http\Controllers\Api\CartController::class, 'ArrivalDateTime']);


    Route::post('added/arrival',  [\App\Http\Controllers\Api\CartController::class, 'addedarrival']);





    //arrival time
    Route::get('paymentMethod',[\App\Http\Controllers\Api\CartController::class, 'paymentMethod']);

    Route::post('submitOrder',[\App\Http\Controllers\Api\CartController::class, 'submitOrder']);


    // get the pervious order
    Route::get('previous/order',[\App\Http\Controllers\Api\CartController::class, 'previousOrder']);
    Route::post('add/comment',  [\App\Http\Controllers\Api\CartController::class, 'addComment']);


    Route::get('pages',      [\App\Http\Controllers\Api\MainController::class, 'Getpage']); // activeation code
    Route::get('pages/{id}', [\App\Http\Controllers\Api\MainController::class, 'page']); // activeation code
    Route::get('social',     [\App\Http\Controllers\Api\MainController::class, 'social']); // activeation code



    Route::post('add/product',     [\App\Http\Controllers\Api\MainController::class, 'addproduct']); // activeation code
    Route::post('delete/Account',     [\App\Http\Controllers\Api\MainController::class, 'deleteAccount']); // activeation code






    #==================================end cart =========================================#




    #==================================admin panel =========================================#

    Route::get('main/page',     [\App\Http\Controllers\Api\adminpanelController::class, 'mainPage']); // activeation code
    Route::post('change/payment',     [\App\Http\Controllers\Api\adminpanelController::class, 'changepayment']); // activeation code


    Route::get('reports',     [\App\Http\Controllers\Api\adminpanelController::class, 'reports']); // activeation code



    //categories
    Route::get('get/category',     [\App\Http\Controllers\Api\adminpanelController::class, 'getCategory']); // activeation code
    Route::post('add/category',     [\App\Http\Controllers\Api\adminpanelController::class, 'addCategory']); // activeation code
    Route::post('delete/category',     [\App\Http\Controllers\Api\adminpanelController::class, 'deleteCategory']); // activeation code
    Route::post('edit/category',     [\App\Http\Controllers\Api\adminpanelController::class, 'editCategory']); // activeation code
    Route::post('active/category',     [\App\Http\Controllers\Api\adminpanelController::class, 'activeCategory']); // activeation code


    // banners
    Route::get('get/brand',     [\App\Http\Controllers\Api\adminpanelController::class, 'getbrands']); // activeation code
    Route::post('add/brand',     [\App\Http\Controllers\Api\adminpanelController::class, 'addBrands']); // activeation code
    Route::post('delete/brand',     [\App\Http\Controllers\Api\adminpanelController::class, 'deletebrand']); // activeation code
    Route::post('edit/brand',     [\App\Http\Controllers\Api\adminpanelController::class, 'editbrand']); // activeation code
    Route::post('active/brand',     [\App\Http\Controllers\Api\adminpanelController::class, 'activebrand']); // activeation code


        // products
    Route::get('get/product',     [\App\Http\Controllers\Api\adminpanelController::class, 'getProduct']); // activeation code
    Route::post('add/product',     [\App\Http\Controllers\Api\adminpanelController::class, 'addProduct']); // activeation code
    Route::post('delete/product',     [\App\Http\Controllers\Api\adminpanelController::class, 'deleteProduct']); // activeation code
    Route::post('update/product',     [\App\Http\Controllers\Api\adminpanelController::class, 'editproduct']); // activeation code
    Route::post('active/product',     [\App\Http\Controllers\Api\adminpanelController::class, 'activeProduct']); // activeation code



    Route::get('get/tager',     [\App\Http\Controllers\Api\adminpanelController::class, 'getTager']); // activeation code
    Route::post('add/tager',     [\App\Http\Controllers\Api\adminpanelController::class, 'addTager']); // activeation code
    Route::post('update/tager',     [\App\Http\Controllers\Api\adminpanelController::class, 'updateTager']); // activeation code
    Route::post('delete/tager',     [\App\Http\Controllers\Api\adminpanelController::class, 'deleteTager']); // activeation code
    Route::post('active/tager',     [\App\Http\Controllers\Api\adminpanelController::class, 'activeTager']); // activeation code


    Route::get('get/users',     [\App\Http\Controllers\Api\adminpanelController::class, 'getusers']); // activeation code
    Route::post('edit/users',     [\App\Http\Controllers\Api\adminpanelController::class, 'editusers']); // activeation code





    Route::get('get/sliders',     [\App\Http\Controllers\Api\adminpanelController::class, 'getsliders']); // activeation code
    Route::post('add/sliders',     [\App\Http\Controllers\Api\adminpanelController::class, 'addsliders']); // activeation code
    Route::post('update/sliders',     [\App\Http\Controllers\Api\adminpanelController::class, 'updatesliders']); // activeation code
    Route::post('delete/sliders',     [\App\Http\Controllers\Api\adminpanelController::class, 'deletesliders']); // activeation code
    Route::post('active/sliders',     [\App\Http\Controllers\Api\adminpanelController::class, 'activesliders']); // activeation code



    Route::get('get/notifcations',     [\App\Http\Controllers\Api\adminpanelController::class, 'getnotifcations']); // activeation code
    Route::post('send/notifcations',     [\App\Http\Controllers\Api\adminpanelController::class, 'addnotifcations']); // activeation code
    Route::post('delete/notifcations',     [\App\Http\Controllers\Api\adminpanelController::class, 'deletenotifcationss']); // activeation code



    Route::get('get/coupons',     [\App\Http\Controllers\Api\adminpanelController::class, 'getcoupons']); // activeation code
    Route::post('add/coupons',     [\App\Http\Controllers\Api\adminpanelController::class, 'addcoupons']); // activeation code
    Route::post('update/coupons',     [\App\Http\Controllers\Api\adminpanelController::class, 'updatecoupons']); // activeation code
    Route::post('delete/coupons',     [\App\Http\Controllers\Api\adminpanelController::class, 'deletecoupons']); // activeation code
    Route::post('active/coupons',     [\App\Http\Controllers\Api\adminpanelController::class, 'activecoupons']); // activeation code



    Route::get('get/orders',     [\App\Http\Controllers\Api\adminpanelController::class, 'previousOrder']); // activeation code
    Route::post('change/orders',     [\App\Http\Controllers\Api\adminpanelController::class, 'changeOrder']); // activeation code


    Route::get('settings',     [\App\Http\Controllers\Api\adminpanelController::class, 'getsettings']); // activeation code
    Route::post('settings',     [\App\Http\Controllers\Api\adminpanelController::class, 'settings']); // activeation code

    Route::get('Children/categories',     [\App\Http\Controllers\Api\adminpanelController::class, 'categoriesChilderen']); // activeation code






    Route::get('get/countries',     [\App\Http\Controllers\Api\adminpanelController::class, 'getcountries']); // activeation code
    Route::post('add/countries',     [\App\Http\Controllers\Api\adminpanelController::class, 'addcountries']); // activeation code
    Route::post('update/countries',     [\App\Http\Controllers\Api\adminpanelController::class, 'updatecountries']); // activeation code
    Route::post('delete/countries',     [\App\Http\Controllers\Api\adminpanelController::class, 'deletecountries']); // activeation code
    Route::post('active/countries',     [\App\Http\Controllers\Api\adminpanelController::class, 'activecountries']); // activeation code


    Route::get('get/features',     [\App\Http\Controllers\Api\adminpanelController::class, 'featureget']); // activeation code
    Route::post('add/features',     [\App\Http\Controllers\Api\adminpanelController::class, 'featurePost']); // activeation code
    Route::post('update/features',     [\App\Http\Controllers\Api\adminpanelController::class, 'featureupdate']); // activeation code
    Route::post('delete/features',     [\App\Http\Controllers\Api\adminpanelController::class, 'featuredelete']); // activeation code


    Route::get('get/payment_metd',     [\App\Http\Controllers\Api\adminpanelController::class, 'getpayment_metd']); // activeation code
    Route::post('store/payment_metd',     [\App\Http\Controllers\Api\adminpanelController::class, 'payment_metdstore']); // activeation code
    Route::post('update/payment_metd',     [\App\Http\Controllers\Api\adminpanelController::class, 'payment_metd']); // activeation code
    Route::post('delete/payment_metd',     [\App\Http\Controllers\Api\adminpanelController::class, 'deletepayment_metd']); // activeation code
    Route::post('active/payment_metd',     [\App\Http\Controllers\Api\adminpanelController::class, 'activepayment_metd']); // activeation code






});
