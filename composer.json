{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "anlutro/l4-settings": "^1.1", "astrotomic/laravel-translatable": "^11.10", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.7", "kreait/laravel-firebase": "^3.1", "laravel/framework": "^8.75", "laravel/helpers": "^1.5", "laravel/passport": "^10.4", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "laravel/ui": "^3.4", "mcamara/laravel-localization": "^1.7", "nesbot/carbon": "^2.56", "niklasravnsborg/laravel-pdf": "*", "santigarcor/laratrust": "^6.3", "tarikhagustia/laravel-mt5": "^1.4", "yajra/laravel-datatables": "1.5", "yajra/laravel-datatables-buttons": "^4.13", "yajra/laravel-datatables-oracle": "~9.0"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false}, "minimum-stability": "dev", "prefer-stable": true}